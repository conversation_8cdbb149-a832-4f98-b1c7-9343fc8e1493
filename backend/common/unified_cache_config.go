package common

import (
	"fmt"
	"time"

	"github.com/rclone/rclone/fs"
)

// UnifiedCacheConfig 统一的缓存配置结构
// 用于标准化123网盘和115网盘的缓存配置，提高一致性和可维护性
type UnifiedCacheConfig struct {
	// 基础缓存TTL配置 - 统一为5分钟策略
	ParentIDCacheTTL    time.Duration // 父目录ID验证缓存TTL
	DirListCacheTTL     time.Duration // 目录列表缓存TTL
	PathToIDCacheTTL    time.Duration // 路径到ID映射缓存TTL
	MetadataCacheTTL    time.Duration // 文件元数据缓存TTL
	FileIDCacheTTL      time.Duration // 文件ID验证缓存TTL
	DownloadURLCacheTTL time.Duration // 下载URL缓存TTL（123网盘特有）

	// 扩展缓存配置
	NetworkSpeedCacheTTL time.Duration // 网络速度缓存TTL
	MD5CacheTTL          time.Duration // MD5缓存TTL（跨云传输用）
	PickCodeCacheTTL     time.Duration // PickCode缓存TTL（115网盘特有）

	// 缓存清理配置
	CleanupInterval time.Duration // 缓存清理间隔
	CleanupTimeout  time.Duration // 缓存清理超时

	// 内存优化和智能清理配置 - 新增
	MaxCacheSize       fs.SizeSuffix // 最大缓存大小
	TargetCleanSize    fs.SizeSuffix // 清理目标大小
	MemTableSize       fs.SizeSuffix // BadgerDB内存表大小
	EnableSmartCleanup bool          // 启用智能清理
	CleanupStrategy    string        // 清理策略："lru", "size", "time", "priority_lru"

	// 后端类型标识
	BackendType string // "123" 或 "115"
}

// DefaultUnifiedCacheConfig 返回指定后端类型的默认统一缓存配置
// backendType: "123" 或 "115"
func DefaultUnifiedCacheConfig(backendType string) UnifiedCacheConfig {
	// 统一TTL策略 - 5分钟
	unifiedTTL := 5 * time.Minute

	config := UnifiedCacheConfig{
		// 基础缓存配置 - 所有后端通用
		ParentIDCacheTTL: unifiedTTL,
		DirListCacheTTL:  unifiedTTL,
		PathToIDCacheTTL: unifiedTTL,
		MetadataCacheTTL: unifiedTTL,
		FileIDCacheTTL:   unifiedTTL,

		// 扩展缓存配置
		NetworkSpeedCacheTTL: unifiedTTL,
		MD5CacheTTL:          24 * time.Hour, // MD5缓存保持24小时
		PickCodeCacheTTL:     unifiedTTL,

		// 缓存清理配置
		CleanupInterval: 5 * time.Minute,
		CleanupTimeout:  2 * time.Minute,

		// 内存优化和智能清理配置 - 新增默认值
		MaxCacheSize:       fs.SizeSuffix(100 << 20), // 100MB (与第一阶段优化一致)
		TargetCleanSize:    fs.SizeSuffix(64 << 20),  // 64MB (与第一阶段优化一致)
		MemTableSize:       fs.SizeSuffix(32 << 20),  // 32MB (与第一阶段优化一致)
		EnableSmartCleanup: false,                    // 默认关闭，支持渐进式部署
		CleanupStrategy:    "size",                   // 默认使用大小策略

		// 后端类型
		BackendType: backendType,
	}

	// 后端特定的配置调整
	switch backendType {
	case "123":
		// 123网盘特有配置
		config.DownloadURLCacheTTL = 0 // 动态TTL，根据API返回的过期时间
	case "115":
		// 115网盘特有配置
		config.DownloadURLCacheTTL = unifiedTTL // 115网盘使用固定TTL
	default:
		// 默认配置
		config.DownloadURLCacheTTL = unifiedTTL
	}

	return config
}

// GetTTLForCacheType 根据缓存类型获取对应的TTL
// 提供统一的TTL查询接口
func (c *UnifiedCacheConfig) GetTTLForCacheType(cacheType string) time.Duration {
	switch cacheType {
	case "parent_id":
		return c.ParentIDCacheTTL
	case "dir_list":
		return c.DirListCacheTTL
	case "path_to_id":
		return c.PathToIDCacheTTL
	case "metadata":
		return c.MetadataCacheTTL
	case "file_id":
		return c.FileIDCacheTTL
	case "download_url":
		return c.DownloadURLCacheTTL
	case "network_speed":
		return c.NetworkSpeedCacheTTL
	case "md5":
		return c.MD5CacheTTL
	case "pick_code":
		return c.PickCodeCacheTTL
	default:
		// 默认返回统一TTL
		return 5 * time.Minute
	}
}

// SetTTLForCacheType 设置指定缓存类型的TTL
// 提供统一的TTL设置接口
func (c *UnifiedCacheConfig) SetTTLForCacheType(cacheType string, ttl time.Duration) {
	switch cacheType {
	case "parent_id":
		c.ParentIDCacheTTL = ttl
	case "dir_list":
		c.DirListCacheTTL = ttl
	case "path_to_id":
		c.PathToIDCacheTTL = ttl
	case "metadata":
		c.MetadataCacheTTL = ttl
	case "file_id":
		c.FileIDCacheTTL = ttl
	case "download_url":
		c.DownloadURLCacheTTL = ttl
	case "network_speed":
		c.NetworkSpeedCacheTTL = ttl
	case "md5":
		c.MD5CacheTTL = ttl
	case "pick_code":
		c.PickCodeCacheTTL = ttl
	}
}

// IsUnifiedTTL 检查是否使用统一TTL策略
// 返回true表示所有基础缓存都使用相同的TTL
func (c *UnifiedCacheConfig) IsUnifiedTTL() bool {
	baseTTL := c.ParentIDCacheTTL
	return c.DirListCacheTTL == baseTTL &&
		c.PathToIDCacheTTL == baseTTL &&
		c.MetadataCacheTTL == baseTTL &&
		c.FileIDCacheTTL == baseTTL
}

// GetCacheTypes 获取当前后端支持的缓存类型列表
func (c *UnifiedCacheConfig) GetCacheTypes() []string {
	baseTypes := []string{
		"parent_id",
		"dir_list",
		"path_to_id",
		"metadata",
		"file_id",
		"network_speed",
		"md5",
	}

	switch c.BackendType {
	case "123":
		baseTypes = append(baseTypes, "download_url")
	case "115":
		baseTypes = append(baseTypes, "download_url", "pick_code")
	}

	return baseTypes
}

// Validate 验证缓存配置的有效性
func (c *UnifiedCacheConfig) Validate() error {
	// 检查TTL是否为正值
	if c.ParentIDCacheTTL <= 0 ||
		c.DirListCacheTTL <= 0 ||
		c.PathToIDCacheTTL <= 0 ||
		c.MetadataCacheTTL <= 0 ||
		c.FileIDCacheTTL <= 0 {
		return fmt.Errorf("缓存TTL必须为正值")
	}

	// 检查后端类型
	if c.BackendType != "123" && c.BackendType != "115" {
		return fmt.Errorf("不支持的后端类型: %s", c.BackendType)
	}

	// 检查新增的内存优化配置
	if c.MaxCacheSize <= 0 {
		return fmt.Errorf("最大缓存大小必须为正值")
	}
	if c.TargetCleanSize <= 0 {
		return fmt.Errorf("清理目标大小必须为正值")
	}
	if c.MemTableSize <= 0 {
		return fmt.Errorf("内存表大小必须为正值")
	}
	if c.TargetCleanSize >= c.MaxCacheSize {
		return fmt.Errorf("清理目标大小(%d)必须小于最大缓存大小(%d)", c.TargetCleanSize, c.MaxCacheSize)
	}

	// 检查清理策略
	validStrategies := []string{"size", "lru", "time", "priority_lru"}
	validStrategy := false
	for _, strategy := range validStrategies {
		if c.CleanupStrategy == strategy {
			validStrategy = true
			break
		}
	}
	if !validStrategy {
		return fmt.Errorf("不支持的清理策略: %s，支持的策略: %v", c.CleanupStrategy, validStrategies)
	}

	return nil
}

// Clone 创建配置的深拷贝
func (c *UnifiedCacheConfig) Clone() UnifiedCacheConfig {
	return UnifiedCacheConfig{
		ParentIDCacheTTL:     c.ParentIDCacheTTL,
		DirListCacheTTL:      c.DirListCacheTTL,
		PathToIDCacheTTL:     c.PathToIDCacheTTL,
		MetadataCacheTTL:     c.MetadataCacheTTL,
		FileIDCacheTTL:       c.FileIDCacheTTL,
		DownloadURLCacheTTL:  c.DownloadURLCacheTTL,
		NetworkSpeedCacheTTL: c.NetworkSpeedCacheTTL,
		MD5CacheTTL:          c.MD5CacheTTL,
		PickCodeCacheTTL:     c.PickCodeCacheTTL,
		CleanupInterval:      c.CleanupInterval,
		CleanupTimeout:       c.CleanupTimeout,
		MaxCacheSize:         c.MaxCacheSize,
		TargetCleanSize:      c.TargetCleanSize,
		MemTableSize:         c.MemTableSize,
		EnableSmartCleanup:   c.EnableSmartCleanup,
		CleanupStrategy:      c.CleanupStrategy,
		BackendType:          c.BackendType,
	}
}

// UnifiedBackendConfig 统一的后端配置结构
// 用于标准化123网盘和115网盘的共同配置选项，减少重复代码
type UnifiedBackendConfig struct {
	// 文件操作配置
	ChunkSize      fs.SizeSuffix `json:"chunk_size"`       // 分片大小
	UploadCutoff   fs.SizeSuffix `json:"upload_cutoff"`    // 上传切换阈值
	MaxUploadParts int           `json:"max_upload_parts"` // 最大上传分片数
	ListChunk      int           `json:"list_chunk"`       // 列表分块大小

	// 网络和速率控制配置
	PacerMinSleep fs.Duration `json:"pacer_min_sleep"` // 最小暂停时间

	// 基础配置
	RootFolderID string `json:"root_folder_id"` // 根文件夹ID
	UserAgent    string `json:"user_agent"`     // 用户代理

	// 缓存配置
	CacheMaxSize       fs.SizeSuffix `json:"cache_max_size"`       // 最大缓存大小
	CacheTargetSize    fs.SizeSuffix `json:"cache_target_size"`    // 缓存目标大小
	EnableSmartCleanup bool          `json:"enable_smart_cleanup"` // 启用智能清理
	CleanupStrategy    string        `json:"cleanup_strategy"`     // 清理策略

	// 后端类型标识
	BackendType string `json:"backend_type"` // "123" 或 "115"
}

// NewUnifiedBackendConfig 创建统一的后端配置
func NewUnifiedBackendConfig(backendType string) *UnifiedBackendConfig {
	config := &UnifiedBackendConfig{
		BackendType: backendType,
	}

	// 设置默认值
	switch backendType {
	case "123":
		config.ChunkSize = 10 * fs.Mebi
		config.UploadCutoff = 50 * fs.Mebi
		config.MaxUploadParts = 10000
		config.ListChunk = 1000
		config.PacerMinSleep = fs.Duration(500 * time.Millisecond) // 2 QPS
		config.UserAgent = "123pan/v2.0.0"
		config.CacheMaxSize = 1 * fs.Gibi
		config.CacheTargetSize = 800 * fs.Mebi
		config.EnableSmartCleanup = true
		config.CleanupStrategy = "priority_lru"
	case "115":
		config.ChunkSize = 10 * fs.Mebi
		config.UploadCutoff = 50 * fs.Mebi
		config.MaxUploadParts = 10000
		config.ListChunk = 1000
		config.PacerMinSleep = fs.Duration(250 * time.Millisecond) // 4 QPS
		config.UserAgent = "115disk/v2.0.0"
		config.CacheMaxSize = 1 * fs.Gibi
		config.CacheTargetSize = 800 * fs.Mebi
		config.EnableSmartCleanup = true
		config.CleanupStrategy = "priority_lru"
	default:
		// 通用默认值
		config.ChunkSize = 10 * fs.Mebi
		config.UploadCutoff = 50 * fs.Mebi
		config.MaxUploadParts = 10000
		config.ListChunk = 1000
		config.PacerMinSleep = fs.Duration(500 * time.Millisecond)
		config.CacheMaxSize = 1 * fs.Gibi
		config.CacheTargetSize = 800 * fs.Mebi
		config.EnableSmartCleanup = true
		config.CleanupStrategy = "priority_lru"
	}

	return config
}

// ValidateUnifiedBackendConfig 验证统一后端配置的有效性
func (c *UnifiedBackendConfig) ValidateUnifiedBackendConfig() error {
	if c.BackendType == "" {
		return fmt.Errorf("后端类型不能为空")
	}

	if c.BackendType != "123" && c.BackendType != "115" {
		return fmt.Errorf("不支持的后端类型: %s", c.BackendType)
	}

	// 验证分片大小
	if c.ChunkSize < 1*fs.Mebi {
		return fmt.Errorf("分片大小不能小于1MB，当前值: %s", c.ChunkSize)
	}
	if c.ChunkSize > 100*fs.Mebi {
		return fmt.Errorf("分片大小不能大于100MB，当前值: %s", c.ChunkSize)
	}

	// 验证上传切换阈值
	if c.UploadCutoff < 0 {
		return fmt.Errorf("上传切换阈值不能为负数，当前值: %s", c.UploadCutoff)
	}

	// 验证最大上传分片数
	if c.MaxUploadParts < 1 || c.MaxUploadParts > 10000 {
		return fmt.Errorf("最大上传分片数必须在1-10000之间，当前值: %d", c.MaxUploadParts)
	}

	// 验证列表分块大小
	if c.ListChunk < 1 || c.ListChunk > 10000 {
		return fmt.Errorf("列表分块大小必须在1-10000之间，当前值: %d", c.ListChunk)
	}

	// 验证最小暂停时间
	if c.PacerMinSleep < 0 {
		return fmt.Errorf("最小暂停时间不能为负数，当前值: %s", c.PacerMinSleep)
	}

	// 验证缓存大小
	if c.CacheMaxSize < 0 {
		return fmt.Errorf("最大缓存大小不能为负数，当前值: %s", c.CacheMaxSize)
	}
	if c.CacheTargetSize < 0 {
		return fmt.Errorf("缓存目标大小不能为负数，当前值: %s", c.CacheTargetSize)
	}
	if c.CacheTargetSize > c.CacheMaxSize {
		return fmt.Errorf("缓存目标大小不能大于最大缓存大小，目标: %s，最大: %s", c.CacheTargetSize, c.CacheMaxSize)
	}

	// 验证清理策略
	validStrategies := []string{"size", "lru", "time", "priority_lru"}
	validStrategy := false
	for _, strategy := range validStrategies {
		if c.CleanupStrategy == strategy {
			validStrategy = true
			break
		}
	}
	if !validStrategy {
		return fmt.Errorf("不支持的清理策略: %s，支持的策略: %v", c.CleanupStrategy, validStrategies)
	}

	return nil
}

// ApplyToBackendOptions 将统一配置应用到具体后端的配置选项
// 这个方法使用反射来设置对应的字段，保持向后兼容性
func (c *UnifiedBackendConfig) ApplyToBackendOptions(backendOptions interface{}) error {
	// 这里使用简单的类型断言而不是反射，以保持代码简洁
	// 具体的映射逻辑在各个后端的初始化函数中实现
	return nil
}

// GetConfigSummary 获取配置摘要信息，用于日志和调试
func (c *UnifiedBackendConfig) GetConfigSummary() string {
	return fmt.Sprintf("后端类型: %s | 分片大小: %s | 上传阈值: %s | 最大分片数: %d | 列表分块: %d | 暂停时间: %s | 缓存大小: %s/%s",
		c.BackendType,
		c.ChunkSize,
		c.UploadCutoff,
		c.MaxUploadParts,
		c.ListChunk,
		c.PacerMinSleep,
		c.CacheTargetSize,
		c.CacheMaxSize,
	)
}

// IsLargeFileUpload 判断是否应该使用大文件上传模式
func (c *UnifiedBackendConfig) IsLargeFileUpload(fileSize int64) bool {
	return fs.SizeSuffix(fileSize) > c.UploadCutoff
}

// GetOptimalChunkCount 根据文件大小计算最优分片数量
func (c *UnifiedBackendConfig) GetOptimalChunkCount(fileSize int64) int {
	if fileSize <= 0 {
		return 1
	}

	chunkCount := int((fileSize + int64(c.ChunkSize) - 1) / int64(c.ChunkSize))
	if chunkCount > c.MaxUploadParts {
		return c.MaxUploadParts
	}
	if chunkCount < 1 {
		return 1
	}
	return chunkCount
}

// GetAdjustedChunkSize 根据文件大小调整分片大小
func (c *UnifiedBackendConfig) GetAdjustedChunkSize(fileSize int64) fs.SizeSuffix {
	if fileSize <= 0 {
		return c.ChunkSize
	}

	// 如果文件太大，需要调整分片大小以不超过最大分片数
	maxTotalSize := int64(c.ChunkSize) * int64(c.MaxUploadParts)
	if fileSize > maxTotalSize {
		adjustedChunkSize := (fileSize + int64(c.MaxUploadParts) - 1) / int64(c.MaxUploadParts)
		return fs.SizeSuffix(adjustedChunkSize)
	}

	return c.ChunkSize
}
